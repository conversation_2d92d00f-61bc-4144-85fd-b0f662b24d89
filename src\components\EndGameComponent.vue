<template>
  <div class="q-pa-md text-center">
    <h1 class="text-h3 text-bold text-primary">¡Fin de la Partida! 🎉</h1>
    <p class="text-h5 q-mt-md">Puntuación final: {{ totalScore }} puntos</p>

    <!-- Mensaje personalizado según los puntos -->
    <p class="text-h6 text-weight-bold text-primary q-mt-md">
      {{ scoreMessage }}
    </p>

    <!-- Posición en el ranking -->
    <p v-if="rankingPosition !== null" class="text-h6 q-mt-md">
      Estás en la posición
      <span class="text-primary">{{ rankingPosition }}</span> del ranking.
    </p>

    <div class="row justify-center q-mt-xl">
      <q-btn color="primary" class="q-mt-lg text-h6" @click="startGame"
        >JUGAR DE NUEVO</q-btn
      >
      <q-btn
        color="secondary"
        text-color="primary"
        class="q-mt-lg text-h6"
        @click="goToRanking"
        >IR AL RANKING</q-btn
      >
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { collection, getDocs, query, orderBy } from "firebase/firestore";
import { db } from "src/firebase";

// Props
const props = defineProps(["totalScore", "startGame", "goToRanking"]);

// Mensaje personalizado según la puntuación
const scoreMessage = computed(() => {
  if (props.totalScore < 100) return "Has de estudiar más 📚.";
  if (props.totalScore < 200) return "¡Buen intento! Sigue practicando.";
  if (props.totalScore < 300) return "¡Estás mejorando! 🚀";
  if (props.totalScore < 400) return "¡Genial! Estás entre los mejores. 🌟";
  if (props.totalScore < 500) return "¡Estupendo! Vas en buena dirección. ✨";
  if (props.totalScore < 600) return "¡Maravilloso! Estás sobresaliendo. 🌠";
  if (props.totalScore < 700) return "¡Excelente! Eres todo un experto. 🥇";
  if (props.totalScore < 800) return "¡Asombroso! Estás casi en la cima. 🏔️";
  if (props.totalScore < 900) return "¡Increíble! Te faltan pocos pasos. 🚀";
  if (props.totalScore < 1000) return "¡Épico! Eres casi perfecto. 💎";
  return "¡Perfecto! Eres una leyenda del trivial. 🏆";
});


// Posición en el ranking
const rankingPosition = ref(null);

async function calculateRanking() {
  try {
    const q = query(collection(db, "results"), orderBy("points", "desc"));
    const querySnapshot = await getDocs(q);
    const results = querySnapshot.docs.map((doc) => doc.data());

    // Encuentra la posición del usuario basado en totalScore
    const position =
      results.findIndex((result) => result.points <= props.totalScore) + 1;
    rankingPosition.value = position || results.length + 1;
  } catch (error) {
    console.error("Error al calcular el ranking:", error);
    rankingPosition.value = null;
  }
}

onMounted(() => {
  calculateRanking();
});
</script>
