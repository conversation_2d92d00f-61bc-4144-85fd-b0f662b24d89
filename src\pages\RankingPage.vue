<template>
  <q-page padding class="bg-grey-1">
    <h6 class="q-my-sm text-weight-bold">Ranking</h6>

    <div v-if="loading" class="q-pa-xl text-center rounded-borders q-mt-xl">
      <q-spinner-orbit color="primary" size="70px" class="q-mb-md" />
      <p class="text-h4 text-primary">Cargando...</p>
    </div>

    <div v-else>
      <q-table
        :rows="rankedResults"
        :columns="columns"
        row-key="id"
        dense
        bordered
        :rows-per-page-options="[10]"
        :rows-per-page="10"
        class="my-sticky-header-table text-lg shadow-2 rounded-borders bg-white q-mt-md text-h6"
      >
        <!-- Renderizar la columna de posición -->
        <template v-slot:body-cell-rank="props">
          <q-td :props="props" class="text-center">
            {{ props.row.rank }}
          </q-td>
        </template>

        <template v-slot:body-cell-fecha="props">
          <q-td :props="props" class="text-center">
            {{ formatDate(props.row.fecha) }}
          </q-td>
        </template>
      </q-table>
      <div class="flex justify-between items-center q-mb-md">
        <div class="text-subtitle1 q-my-md">
          Total de partidas jugadas: <b>{{ totalGames }}</b>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { db } from "src/firebase";
import { collection, getDocs, query, orderBy } from "firebase/firestore";

// Estado local
const results = ref([]);
const rankedResults = ref([]);
const totalGames = ref(0);
const loading = ref(true); // Estado de carga

// Definir columnas de la tabla
const columns = [
  {
    name: "rank",
    label: "Rank",
    align: "left",
    field: "rank",
  },
  {
    name: "name",
    required: true,
    label: "Nombre",
    align: "left",
    field: "name",
  },
  {
    name: "points",
    required: true,
    label: "Puntos",
    align: "left",
    field: "points",
    sortable: true,
  },
  {
    name: "fecha",
    required: true,
    label: "Fecha",
    align: "right",
    field: "fecha",
    sortable: true,
  },
];

// Formatear fecha
function formatDate(date) {
  return date ? new Date(date).toLocaleDateString() : "N/A";
}

// Agregar posición (rank) a los resultados
function assignRankToResults(fetchedResults) {
  rankedResults.value = fetchedResults.map((item, index) => ({
    ...item,
    rank: index + 1,
  }));
}

// Consultar datos de Firestore
async function fetchResults() {
  try {
    const q = query(collection(db, "results"), orderBy("points", "desc"));
    const querySnapshot = await getDocs(q);

    const fetchedResults = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    results.value = fetchedResults;
    assignRankToResults(fetchedResults);

    // Calcular estadísticas
    totalGames.value = fetchedResults.reduce((total, result) => {
      return total + (result.attempts || 0); // Sumar todos los "attempts", considerando que algunos pueden no tener el campo
    }, 0);
  } finally {
    loading.value = false; // Terminar el estado de carga
  }
}

// Llamar a `fetchResults` al montar el componente
onMounted(fetchResults);
</script>

<style>
.full-height {
  height: 100vh;
}

thead,
.q-table__bottom {
  background-color: #fd8b51;
  color: white;
}
</style>
