const routes = [
  {
    path: "/",
    component: () => import("layouts/MainLayout.vue"),
    children: [
      { path: "", component: () => import("pages/IndexPage.vue") },
      { path: "play", component: () => import("pages/PlayPage.vue") },
      { path: "ranking", component: () => import("pages/RankingPage.vue") },
      { path: "rules", component: () => import("pages/RulesPage.vue") },
      { path: "about", component: () => import("pages/AboutPage.vue") },
      { path: "create", component: () => import("pages/CreatePage.vue") },
      { path: "privacy", component: () => import("pages/PrivacyPage.vue") },
    ],
  },
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;
