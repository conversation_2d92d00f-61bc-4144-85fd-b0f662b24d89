<template>
  <!-- Div para mostrar cuando se responde correctamente -->
  <div
    v-if="showDescription"
    class="flex column items-center justify-between text-center q-pt-none q-pa-md q-mt-xs"
  >
    <div>
      <p class="text-h6 q-mt-xs" v-html="feedbackMessage"></p>
      <p v-if="image.description" class="text-body1 q-mb-md text-weight-light">
        {{ image.description }}
      </p>
    </div>
    <q-btn
      color="primary"
      class="q-mt-xl button-next"
      @click="startTransition"
      label="Siguiente Carta"
      icon="chevron_right"
      size="lg"
    />
  </div>

  <div v-else class="column items-center justify-center">
    <!-- <div class="row justify-between q-mb-md q-mt-lg font-lg">
      <div v-if="image.difficulty" class="row items-center q-mr-md">
        <p class="q-mb-none q-mr-md text-weight-bold">Nivel:</p>
        <q-chip round color="primary" text-color="white">{{
          image.difficulty
        }}</q-chip>
      </div>
      <div v-if="image.topic" class="row items-center q-mr-md">
        <p class="q-mb-none q-mr-md text-weight-bold">Temática:</p>
        <q-chip round color="primary" text-color="white">{{
          image.topic
        }}</q-chip>
      </div>
    </div> -->

    <q-img
      v-if="image"
      :src="image.image"
      :alt="'Imagen de ' + image.topic"
      ratio="1"
      class="rounded-borders"
      style="
        width: 100%;
        width: 320px;
        border-style: solid;
        border-color: #fd8b51;
      "
    />

    <div
      class="flex column items-center q-mt-md justify-center"
      style="width: 100%"
    >
      <q-slider
        v-model="sliderYear"
        :min="yearRange.min"
        :max="yearRange.max"
        :step="1"
        label-class="slider-label"
        color="primary"
        :disable="inputDisabled"
        class="q-mt-md q-mb-md"
        style="width: 80%; max-width: 320px"
        track-size="9px"
        track-color="grey"
        inner-track-color="transparent"
        thumb-size="35px"
      />
    </div>
    <div
      class="flex justify-between items-between"
      style="width: 100%; max-width: 320px"
    >
      <q-chip
        class="cursor-pointer text-h5 q-mt-none q-mb-none q-mr-lg text-right bg-secondary"
      >
        {{ sliderYear }}
        <q-tooltip> ¡Mueve el círculo! </q-tooltip>
      </q-chip>
      <q-btn color="primary" size="lg" @click="checkAnswer">¡ACIERTA!</q-btn>
    </div>

    <q-icon
      v-if="showIcon"
      :name="iconSymbol"
      :color="iconColor"
      size="lg"
      class="q-mt-md"
    />

    <div class="row justify-center q-mb-lg">
      <q-btn
        v-if="inputDisabled"
        color="secondary"
        class="q-mt-md"
        @click="showDescription = true"
        label="Mostrar Respuesta"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from "vue";

// Props y emit
const props = defineProps({
  image: Object,
});

const emit = defineEmits([
  "correct-answer",
  "increment-mistake",
  "update-score",
  "update-time",
]);

// Estado local
const userGuess = ref("");
const showIcon = ref(false);
const inputColor = ref("");
const iconSymbol = ref("");
const iconColor = ref("");
const timeLeft = ref(20); // Tiempo en segundos
const maxScore = ref(0); // Puntos máximos alcanzados por pregunta
let timer;
const showDescription = ref(false); // Estado para mostrar la descripción
const sliderYear = ref(1965); // Año inicial del slider
const yearRange = ref({ min: 1965, max: 2025 }); // Rango de años para el slider
const inputDisabled = ref(false); // Controla si el input está bloqueado
const currentScore = ref(0); // Puntuación de la pregunta actual
const feedbackMessage = ref(""); // Mensaje de retroalimentación

// Método para verificar la respuesta
function checkAnswer() {
  const correctYear = parseInt(props.image.year); // Año correcto
  const guessedYear = sliderYear.value; // Año seleccionado con el slider
  const difference = Math.abs(guessedYear - correctYear);

  if (isNaN(guessedYear)) {
    feedbackMessage.value = "Por favor, ingresa un año válido.";
    inputColor.value = "negative";
    return;
  }

  // Calcular puntos según la diferencia
  if (difference === 0) {
    currentScore.value = 100;
    feedbackMessage.value = `¡Maravilloso, mejor imposible! 🎉 100 puntos. La respuesta es ${correctYear}.`;
  } else if (difference === 1) {
    currentScore.value = 80;
    feedbackMessage.value = `¡Fantástico! Casi la clavas. 80 puntos. La respuesta es ${correctYear}.`;
  } else if (difference === 2) {
    currentScore.value = 60;
    feedbackMessage.value = `¡Muy bien! Te quedaste a un pelo. 60 puntos. La respuesta es ${correctYear}.`;
  } else if (difference <= 4) {
    currentScore.value = 50;
    feedbackMessage.value = `¡Bravo! Bastante cerca. 50 puntos. La respuesta es ${correctYear}.`;
  } else if (difference <= 6) {
    currentScore.value = 40;
    feedbackMessage.value = `¡Bastante bien! No ibas desencaminado. 40 puntos. La respuesta es ${correctYear}.`;
  } else if (difference <= 9) {
    currentScore.value = 30;
    feedbackMessage.value = `No está mal, pero puedes mejorar. 30 puntos. La respuesta es ${correctYear}.`;
  } else if (difference <= 12) {
    currentScore.value = 20;
    feedbackMessage.value = `Lejos, pero muchos lo hacen peor. 20 puntos. La respuesta es ${correctYear}.`;
  } else if (difference <= 15) {
    currentScore.value = 10;
    feedbackMessage.value = `Bastante mal, tienes que practicar. 10 puntos. La respuesta es ${correctYear}.`;
  } else {
    currentScore.value = 0;
    feedbackMessage.value = `¡Fatal! Necesitas ayuda. 0 puntos. La respuesta es ${correctYear}.`;
  }

  // Actualizar puntuación total
  maxScore.value += currentScore.value;
  emitFinalScore();

  // Mostrar la descripción y bloquear el input
  inputDisabled.value = true;
  showDescription.value = true; // Mostrar descripción
  timeLeft.value = 20; // Reiniciar el tiempo
  emit("update-time", timeLeft.value); // Emitir tiempo actualizado
  clearTimer();
}

// Emite el puntaje máximo alcanzado al componente principal
function emitFinalScore() {
  emit("update-score", maxScore.value);
}

// Método para avanzar a la transición
function startTransition() {
  showDescription.value = false; // Ocultar descripción
  inputDisabled.value = false; // Habilitar el input
  feedbackMessage.value = ""; // Limpiar el mensaje de retroalimentación
  userGuess.value = ""; // Limpiar el input
  emit("correct-answer"); // Emitir evento para cargar la siguiente pregunta
}

// Temporizador de cuenta regresiva
// Temporizador de cuenta regresiva con progreso fluido
function startTimer() {
  clearTimer();
  const interval = 50; // Intervalo de actualización en milisegundos
  const decrement = 1000 / interval; // Reducir proporcionalmente al intervalo
  let remainingTime = 20 * decrement; // Tiempo total en unidades menores

  timer = setInterval(() => {
    if (remainingTime > 0) {
      remainingTime--;
      timeLeft.value = (remainingTime / decrement).toFixed(2); // Actualizar `timeLeft` de forma progresiva
      emit("update-time", parseFloat(timeLeft.value)); // Emitir tiempo restante
    } else {
      clearTimer();
      timeLeft.value = 0;
      emit("update-time", 0); // Emitir tiempo final
      showDescription.value = true;
      feedbackMessage.value = `Se te acabó el tiempo...`;

      inputDisabled.value = true;
    }
  }, interval);
}

// Detener temporizador
function clearTimer() {
  if (timer) {
    clearInterval(timer);
  }
}

// Limpiar el temporizador al desmontar
onMounted(startTimer);
onBeforeUnmount(clearTimer);

watch(
  () => props.image,
  () => {
    showDescription.value = false; // Ocultar descripción
    inputDisabled.value = false; // Habilitar el input
    timeLeft.value = 20; // Reiniciar tiempo
    emit("update-time", timeLeft.value); // Emitir tiempo inicial
    startTimer();
  }
);
</script>

<style scoped>
.restart-next {
  position: fixed;
  bottom: 10px;
  left: 20px;
  z-index: 1000;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}
</style>
