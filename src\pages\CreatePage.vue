<template>
  <q-page padding class="bg-grey-1">
    <q-dialog v-model="passwordModalVisible" persistent>
      <q-card class="q-pa-md">
        <h3 class="text-h6 text-center">Acceso Restringido</h3>
        <q-input
          v-model="passwordInput"
          label="Ingresa la contraseña maestra"
          type="password"
          filled
          class="q-mb-md"
        />
        <q-btn
          label="Acceder"
          color="primary"
          class="full-width q-mt-md"
          @click="validatePassword"
        />
      </q-card>
    </q-dialog>
    <div v-if="isAuthorized">
      <h6 class="q-my-none">Administrar cartas</h6>

      <!-- Mostrar Spinner mientras se cargan las imágenes -->
      <div
        v-if="isLoading"
        class="flex column justify-center items-center q-mt-md"
      >
        <q-spinner-orbit color="primary" size="70px" />
        <p class="text-h6 text-primary q-ml-md">Cargando...</p>
      </div>

      <div v-else>
        <!-- Tabs -->
        <q-tabs v-model="activeTab" class="text-primary" align="left" dense>
          <q-tab name="list" label="Lista de Cartas" icon="image" />
          <q-tab name="create" label="Crear Carta" icon="add" />
          <q-tab name="config" label="Configuración" icon="settings" />
        </q-tabs>

        <q-separator />

        <!-- Contenido del tab Lista -->
        <div v-if="activeTab === 'list'" class="q-mt-md">
          <h3 class="text-h6 text-bold">Cartas Subidas</h3>
          <div class="row wrap justify-start">
            <q-card
              v-for="image in paginatedImages"
              :key="image.id"
              class="q-pa-sm q-ma-sm"
              style="width: 150px"
            >
              <q-img
                :src="image.image"
                :alt="'Imagen de ' + image.topic"
                ratio="1"
                class="rounded-borders"
              />
              <div class="text-left q-mt-sm">
                <p class="text-caption q-my-xs">
                  <span class="text-weight-bold">Tema:</span> {{ image.topic }}
                </p>
                <p class="text-caption q-my-xs">
                  <span class="text-weight-bold">Año:</span> {{ image.year }}
                </p>
                <p class="text-caption q-my-xs">
                  <span class="text-weight-bold">Nivel: </span
                  >{{ image.difficulty }}
                </p>

                <!-- Descripción con truncado -->
                <div>
                  <p
                    class="text-caption description"
                    :class="{ expanded: expandedDescriptions[image.id] }"
                  >
                    <span class="text-weight-bold"> Descripción:</span>
                    {{ image.description }}
                  </p>
                  <q-btn
                    flat
                    dense
                    color="primary"
                    :label="
                      expandedDescriptions[image.id] ? 'Ver menos' : 'Ver más'
                    "
                    @click="toggleDescription(image.id)"
                  />
                </div>

                <!-- Botones de edición y eliminación -->
                <q-btn
                  flat
                  dense
                  icon="edit"
                  color="primary"
                  @click="openEditModal(image)"
                />
                <q-btn
                  flat
                  dense
                  icon="delete"
                  color="negative"
                  @click="openDeleteModal(image)"
                />
              </div>
            </q-card>
          </div>
          <q-pagination
            v-model="currentPage"
            :max="totalPages"
            boundary-numbers
            color="primary"
            class="q-mt-md"
          />
        </div>

        <!-- Contenido del tab Crear -->
        <div v-if="activeTab === 'create'" class="q-mt-md">
          <h3 class="text-h5 q-my-none text-bold">Subir carta</h3>

          <q-form @submit.prevent="handleSubmit">
            <q-input
              v-model="year"
              label="Año"
              type="number"
              :rules="[(v) => !!v || 'El año es obligatorio']"
              filled
              class="q-mb-md"
            />

            <q-select
              v-model="difficulty"
              :options="difficultyOptions"
              label="Dificultad"
              filled
              class="q-mb-md"
            />

            <q-input
              v-model="description"
              label="Descripcion"
              filled
              class="q-mb-md"
              type="textarea"
            />

            <!-- Tema -->
            <q-select
              v-model="topic"
              :options="topicOptions"
              label="Tema"
              filled
              class="q-mb-md"
            />

            <input
              type="file"
              accept="image/*"
              @change="handleFileChange"
              class="q-mb-md"
            />

            <q-btn
              type="submit"
              label="Subir"
              color="primary"
              :loading="loading"
              class="full-width"
            />
          </q-form>
        </div>
        <div v-if="activeTab === 'config'" class="q-mt-md">
          <h3 class="text-h5 q-my-none text-bold">Configuración General</h3>

          <q-form @submit.prevent="updateConfig">
            <q-input
              v-model.number="config.maxImages"
              label="Número máximo de cartas (MAX_IMAGES)"
              type="number"
              :rules="[(v) => v > 0 || 'Debe ser un número mayor a 0']"
              filled
              class="q-mb-md"
            />
            <q-btn
              type="submit"
              label="Guardar Configuración"
              color="primary"
            />
          </q-form>
        </div>
      </div>
    </div>
    <q-dialog v-model="editModalVisible">
      <q-card class="q-pa-md">
        <h3 class="text-h6">Editar Carta</h3>
        <q-input v-model="editImage.year" label="Año" filled class="q-mb-md" />
        <q-select
          v-model="editImage.difficulty"
          :options="difficultyOptions"
          label="Dificultad"
          filled
          class="q-mb-md"
        />
        <q-select
          v-model="editImage.topic"
          :options="topicOptions"
          label="Tema"
          filled
          class="q-mb-md"
        />
        <q-input
          v-model="editImage.description"
          label="Descripción"
          type="textarea"
          filled
          class="q-mb-md"
        />
        <input
          type="file"
          accept="image/*"
          @change="handleEditFileChange"
          class="q-mb-md"
        />
        <q-btn
          label="Guardar"
          color="primary"
          class="full-width q-mt-md"
          @click="saveEdit"
        />
      </q-card>
    </q-dialog>

    <q-dialog v-model="deleteModalVisible">
      <q-card class="q-pa-md">
        <h3 class="text-h6 text-negative">
          ¿Estás seguro de que quieres eliminar esta carta?
        </h3>
        <div class="row justify-end q-mt-md">
          <q-btn flat label="Cancelar" @click="deleteModalVisible = false" />
          <q-btn
            flat
            label="Eliminar"
            color="negative"
            @click="confirmDelete"
          />
        </div>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { db } from "src/firebase";
import {
  collection,
  getDocs,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  getDoc,
} from "firebase/firestore";

// Campos del formulario
const year = ref("");
const file = ref(null);
const difficulty = ref("");
const topic = ref("");
const description = ref("");
const loading = ref(false);
const activeTab = ref("list"); // Por defecto, "list"
const isLoading = ref(true); // Estado para mostrar el spinner
const passwordModalVisible = ref(false);
const passwordInput = ref("");
const isAuthorized = ref(false);

const MASTER_PASSWORD = "antonio_madridejos_1965"; // Cambia esto por tu contraseña

function validatePassword() {
  if (passwordInput.value === MASTER_PASSWORD) {
    localStorage.setItem("masterPasswordCreate", MASTER_PASSWORD);
    isAuthorized.value = true;
    passwordModalVisible.value = false;
  } else {
    alert("Contraseña incorrecta. Inténtalo de nuevo.");
  }
}

onMounted(() => {
  const storedPassword = localStorage.getItem("masterPasswordCreate");
  if (storedPassword === MASTER_PASSWORD) {
    isAuthorized.value = true;
  } else {
    passwordModalVisible.value = true;
  }
});
// Opciones de Dificultad y Tema
const difficultyOptions = ["Fácil", "Media", "Difícil"];
const topicOptions = [
  "Miscelánea",
  "Deporte",
  "Cultura",
  "Ciencia y tecnología",
  "Historia",
];

// Lista de imágenes
const itemsPerPage = 20; // Número de imágenes por página
const currentPage = ref(1); // Página actual
const images = ref([]); // Lista de imágenes cargadas
// Estado para modales
const editModalVisible = ref(false);
const deleteModalVisible = ref(false);
const editImage = ref({}); // Para almacenar la imagen seleccionada para editar
const editFile = ref(null); // Para almacenar un nuevo archivo de imagen seleccionado
const deleteImage = ref({}); // Para almacenar la imagen seleccionada para eliminar

function handleFileChange(event) {
  const selectedFile = event.target.files[0];
  if (selectedFile) {
    file.value = selectedFile;
  } else {
    file.value = null;
  }
}
const expandedDescriptions = ref({});

// Alternar la expansión de la descripción
function toggleDescription(imageId) {
  expandedDescriptions.value[imageId] = !expandedDescriptions.value[imageId];
}

const paginatedImages = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return images.value.slice(startIndex, endIndex);
});

// Calcular el número total de páginas
const totalPages = computed(() =>
  Math.ceil(images.value.length / itemsPerPage)
);

function handleEditFileChange(event) {
  const selectedFile = event.target.files[0];
  if (selectedFile) {
    editFile.value = selectedFile;
  } else {
    editFile.value = null;
  }
}

async function handleSubmit() {
  if (!year.value || !file.value) {
    console.error("Todos los campos son obligatorios.");
    return;
  }

  loading.value = true;

  try {
    const reader = new FileReader();
    reader.readAsDataURL(file.value);

    reader.onload = async () => {
      const base64Image = reader.result;

      const docRef = doc(db, "images", `${Date.now()}`);
      await setDoc(docRef, {
        year: year.value,
        difficulty: difficulty.value,
        topic: topic.value,
        description: description.value,
        image: base64Image,
        random: Math.random(),
      });

      alert("Imagen subida correctamente.");
      await loadImages();

      // Resetear campos del formulario
      year.value = "";
      difficulty.value = "";
      topic.value = "";
      description.value = "";
      file.value = null;
    };

    reader.onerror = (error) => {
      console.error("Error al convertir la imagen a Base64:", error);
    };
  } catch (error) {
    console.error("Error al subir los datos a Firestore:", error);
  } finally {
    loading.value = false;
  }
}

// Función para abrir modal de edición
function openEditModal(image) {
  editImage.value = { ...image };
  editModalVisible.value = true;
}

// Guardar cambios en la imagen editada
async function saveEdit() {
  try {
    const docRef = doc(db, "images", editImage.value.id);
    const updatedData = {
      year: editImage.value.year,
      difficulty: editImage.value.difficulty,
      topic: editImage.value.topic,
      description: editImage.value.description,
    };

    if (editFile.value) {
      const reader = new FileReader();
      reader.readAsDataURL(editFile.value);

      const base64Image = await new Promise((resolve) => {
        reader.onload = () => resolve(reader.result);
      });

      updatedData.image = base64Image; // Actualizar la imagen si se seleccionó una nueva
    }

    await updateDoc(docRef, updatedData);
    alert("Imagen actualizada correctamente.");
    editModalVisible.value = false;
    await loadImages();
  } catch (error) {
    console.error("Error al actualizar la imagen:", error);
  }
}

// function toggleDescription(imageId) {
//   expandedDescriptions.value[imageId] = !expandedDescriptions.value[imageId];
// }

// Función para abrir modal de eliminación
function openDeleteModal(image) {
  deleteImage.value = { ...image };
  deleteModalVisible.value = true;
}

// Confirmar eliminación
async function confirmDelete() {
  try {
    const docRef = doc(db, "images", deleteImage.value.id);
    await deleteDoc(docRef);
    alert("Imagen eliminada correctamente.");
    deleteModalVisible.value = false;
    await loadImages();
  } catch (error) {
    console.error("Error al eliminar la imagen:", error);
  }
}

// Cargar imágenes desde Firestore
async function loadImages() {
  try {
    const querySnapshot = await getDocs(collection(db, "images"));
    images.value = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    console.error("Error al cargar imágenes desde Firestore:", error);
  } finally {
    isLoading.value = false; // Desactivar el loading una vez que se carguen las imágenes
  }
}

// Cargar imágenes al montar el componente
onMounted(() => {
  loadImages();
});

const config = ref({ maxImages: 5 });
const isLoadingConfig = ref(true);

// Cargar configuración desde Firestore
async function loadConfig() {
  try {
    const docRef = doc(db, "config", "settings");
    const docSnapshot = await getDoc(docRef);
    if (docSnapshot.exists()) {
      config.value = docSnapshot.data();
    } else {
      console.warn(
        "No se encontró la configuración. Usando valores por defecto."
      );
    }
  } catch (error) {
    console.error("Error al cargar la configuración:", error);
  } finally {
    isLoadingConfig.value = false;
  }
}

// Actualizar configuración en Firestore
async function updateConfig() {
  try {
    const docRef = doc(db, "config", "settings");
    await setDoc(docRef, config.value);
    alert("Configuración actualizada correctamente.");
  } catch (error) {
    console.error("Error al actualizar la configuración:", error);
  }
}

// Cargar configuración al montar
onMounted(() => {
  loadConfig();
});
</script>

<style scoped>
.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.description {
  max-height: 50px;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.description.expanded {
  max-height: none; /* Elimina la restricción cuando está expandida */
}
</style>
