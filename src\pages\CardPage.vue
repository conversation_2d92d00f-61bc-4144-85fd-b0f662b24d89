<template>
  <q-page padding class="bg-grey-1">
    <q-dialog v-model="nameModalVisible" persistent>
      <q-card class="q-pa-md">
        <h3 class="text-h6 text-center">Introduce tu nombre</h3>
        <q-input
          v-model="userName"
          label="Nombre de usuario"
          type="text"
          filled
          class="q-mb-md"
        />
        <q-btn
          label="Guardar y Jugar"
          color="primary"
          class="full-width q-mt-md"
          @click="saveUserNameAndStartGame"
        />
      </q-card>
    </q-dialog>

    <!-- Header fijo -->
    <div
      v-if="gameStarted && !gameOver && !isStarting && !transitioning"
      class="q-px-sm text-primary q-pa-none q-mb-md"
    >
      <div class="row justify-between items-center q-mb-xs q-mt-xs">
        <p class="text-h6 text-weight-bold q-mb-none">Puntos: {{ totalScore }}</p>
        <q-space></q-space>
        <q-chip class="text-subtitle1 q-mt-none q-mb-none q-mr-md bg-secondary">
          Carta {{ currentImageIndex + 1 }}/{{ config.maxImages }}
        </q-chip>
        <q-circular-progress
          :value="timeLeft"
          size="55px"
          color="primary"
          :show-value="true"
          :reverse="true"
          :max="20"
          font-size="23px"
        >
          {{ roundedTimeLeft }}
        </q-circular-progress>
      </div>
    </div>

    <!-- Preparando el juego -->
    <div v-if="isStarting" class="q-pa-xl text-center rounded-borders q-mt-xl">
      <q-spinner-orbit color="primary" size="70px" class="q-mb-md" />
      <p class="text-h4 text-primary">Preparando el trivial...</p>
    </div>

    <!-- Juego activo -->
    <div v-else-if="!gameOver">
      <div
        v-if="transitioning"
        class="q-pa-lg text-center rounded-borders q-mt-xl"
      >
        <q-spinner-orbit color="primary" size="70px" class="q-mb-md q-mt-xl" />
        <p class="text-h4 text-primary">
          Próxima carta en {{ transitionCounter }} segundos...
        </p>
      </div>
      <div v-else>
        <TriviaCard
          :image="currentImage"
          @correct-answer="startTransition"
          @increment-mistake="incrementMistakes"
          @update-score="updateScore"
          @update-time="updateTime"
        />
      </div>
    </div>

    <!-- Fin del juego -->
    <div v-else>
      <EndGameComponent
        :totalScore="totalScore"
        :startGame="startGame"
        :goToRanking="goToRanking"
      />
    </div>

    <q-btn
      v-if="$route.path === '/play' && gameStarted && !gameOver"
      icon="replay"
      color="primary"
      round
      size="md"
      class="restart-button"
      @click="confirmRestartModalVisible = true"
    />

    <q-dialog v-model="confirmRestartModalVisible" persistent>
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6 q-mr-lg">
            ¿Quieres reiniciar?
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section> Perderás todo el progreso actual. </q-card-section>
        <q-card-section>
          <q-btn
            class="q-mt-xl"
            label="Reiniciar"
            color="primary"
            @click="confirmRestart"
          />
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, onMounted } from "vue";
import TriviaCard from "components/TriviaCard.vue";
import { db, auth } from "src/firebase";
import {
  collection,
  getDocs,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  query,
  orderBy,
  limit,
  where,
} from "firebase/firestore";
import { useRouter } from "vue-router";
import HomeComponent from "components/HomeComponent.vue";
import EndGameComponent from "components/EndGameComponent.vue";

const router = useRouter();
const images = ref([]);
const currentImageIndex = ref(0);
const mistakesCount = ref(0);
const totalScore = ref(0);
const gameOver = ref(false);
const gameStarted = ref(false);
const isStarting = ref(false);
const transitioning = ref(false);
const transitionCounter = ref(1);
let transitionTimer;
const totalTime = ref(0);
let totalTimer;
let isTimerRunning = false;
const timeLeft = ref(20);
const userName = ref(localStorage.getItem("localName") || "");
const nameModalVisible = ref(false);
const roundedTimeLeft = computed(() => Math.ceil(timeLeft.value));

function updateTime(newTime) {
  timeLeft.value = newTime;
}

function handlePlayClick() {
  if (!userName.value) {
    nameModalVisible.value = true; // Mostrar el diálogo
  } else {
    startGame(); // Comenzar el juego si ya hay un nombre
  }
}

function saveUserNameAndStartGame() {
  if (userName.value.trim() === "") {
    alert("Por favor, introduce un nombre válido.");
    return;
  }
  localStorage.setItem("localName", userName.value.trim());
  nameModalVisible.value = false;
  startGame();
}

// Cargar imágenes desde Firestore
// Cargar imágenes aleatorias desde Firestore
async function loadImagesFromFirestore() {
  try {
    const randomSeed = Math.random(); // Generar un número aleatorio como punto de partida

    // Consulta imágenes con random >= randomSeed
    const imagesQuery = query(
      collection(db, "images"),
      where("random", ">=", randomSeed),
      orderBy("random"),
      limit(config.value.maxImages)
    );

    let querySnapshot = await getDocs(imagesQuery);

    // Si no hay suficientes resultados, realiza una consulta fallback
    if (querySnapshot.empty || querySnapshot.size < 10) {
      const fallbackQuery = query(
        collection(db, "images"),
        where("random", "<", randomSeed),
        orderBy("random"),
        limit(10 - querySnapshot.size) // Ajustar el límite para completar las 10 imágenes
      );
      const fallbackSnapshot = await getDocs(fallbackQuery);
      querySnapshot = {
        docs: [...querySnapshot.docs, ...fallbackSnapshot.docs],
      };
    }

    // Mapear resultados
    images.value = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    if (images.value.length === 0) {
      console.warn("No se encontraron imágenes en Firestore.");
    }
  } catch (error) {
    console.error(
      "Error al cargar imágenes aleatorias desde Firestore:",
      error
    );
  }
}

// Imagen actual
const currentImage = computed(
  () => images.value[currentImageIndex.value] || null
);

function goToRanking() {
  router.push("/ranking");
}

async function saveResults() {
  const userNameToSave = userName.value;
  if (userNameToSave) {
    const userDocRef = doc(db, "results", userNameToSave);

    try {
      const userDoc = await getDoc(userDocRef);
      const currentDate = new Date(); // Obtener la fecha actual

      if (userDoc.exists()) {
        const existingData = userDoc.data();
        const newPoints = Math.max(existingData.points, totalScore.value);

        await updateDoc(userDocRef, {
          points: newPoints,
          attempts: (existingData.attempts || 0) + 1,
          mistakes: mistakesCount.value,
          fecha: currentDate.toISOString(), // Guardar la fecha actual en formato ISO
        });
      } else {
        await setDoc(userDocRef, {
          name: userNameToSave,
          points: totalScore.value,
          attempts: 1,
          fecha: currentDate.toISOString(), // Guardar la fecha actual en formato ISO
        });
      }
    } catch (error) {
      console.error("Error al guardar resultados:", error);
    }
  } else {
    console.error(
      "No se puede guardar el resultado: no se encontró un nombre de usuario."
    );
  }
}

// Iniciar transición
function startTransition() {
  if (currentImageIndex.value < config.value.maxImages - 1) {
    pauseTimer(); // Pausa el temporizador durante la transición
    transitioning.value = true;
    transitionCounter.value = 2;
    transitionTimer = setInterval(() => {
      if (transitionCounter.value > 0) {
        transitionCounter.value--;
      } else {
        clearInterval(transitionTimer);
        transitioning.value = false;
        loadNextImage();
      }
    }, 1000);
  } else {
    // Juego terminado
    gameOver.value = true;
    stopTimer();
    saveResults();
  }
}

function loadNextImage() {
  if (currentImageIndex.value < config.value.maxImages - 1) {
    currentImageIndex.value++;
  } else {
    // Si se alcanza el máximo, finalizar juego
    gameOver.value = true;
    stopTimer();
    saveResults();
  }
}

// Método para incrementar fallos
function incrementMistakes() {
  mistakesCount.value++;
}

// Método para actualizar puntuación
function updateScore(points) {
  totalScore.value += points;
}

// Método para iniciar el juego
async function startGame() {
  gameStarted.value = true;

  isStarting.value = true;

  setTimeout(async () => {
    isStarting.value = false;
    gameStarted.value = true;
    gameOver.value = false;
    transitioning.value = false;
    currentImageIndex.value = 0;
    mistakesCount.value = 0;
    totalScore.value = 0;
    totalTime.value = 0;

    await loadImagesFromFirestore();

    if (images.value.length > 0) {
      startTimer(); // Iniciar el temporizador si hay imágenes disponibles
    } else {
      console.error("No hay imágenes disponibles para el trivial.");
      gameStarted.value = false;
    }
  }, 3000); // Retraso de 5 segundos
}

// Temporizador principal: iniciar
function startTimer() {
  if (!isTimerRunning) {
    isTimerRunning = true;
    totalTimer = setInterval(() => {
      totalTime.value++;
    }, 1000);
  }
}

// Temporizador principal: pausar
function pauseTimer() {
  if (isTimerRunning) {
    clearInterval(totalTimer);
    isTimerRunning = false;
  }
}

function stopTimer() {
  clearInterval(totalTimer);
  isTimerRunning = false;
}

onBeforeUnmount(() => {
  clearInterval(transitionTimer);
  stopTimer();
});

onMounted(async () => {
  await loadConfig();
  //  await loadImagesFromFirestore();
  //  addRandomFieldToExistingImages()
});

onMounted(() => {
  if (!userName.value) {
    nameModalVisible.value = true; // Mostrar diálogo si no hay usuario registrado
  } else {
    startGame(); // Iniciar juego directamente si hay usuario registrado
  }
});

const confirmRestartModalVisible = ref(false);

function confirmRestart() {
  confirmRestartModalVisible.value = false;
  restartGame(); // Llama al método de reinicio existente
}

function restartGame() {
  // Reinicia todos los valores necesarios para empezar desde el principio
  gameStarted.value = false;
  gameOver.value = false;
  isStarting.value = false;
  transitioning.value = false;
  currentImageIndex.value = 0;
  mistakesCount.value = 0;
  totalScore.value = 0;
  timeLeft.value = 20;

  // Opcional: recargar imágenes o resetear el slider
  images.value = [];
  loadImagesFromFirestore();
}

const config = ref(""); // Valor por defecto

async function loadConfig() {
  try {
    const docRef = doc(db, "config", "settings");
    const configSnapshot = await getDoc(docRef);
    if (configSnapshot.exists()) {
      config.value = configSnapshot.data();
    } else {
      console.warn(
        "No se encontró la configuración. Usando valores por defecto."
      );
    }
  } catch (error) {
    console.error("Error al cargar la configuración:", error);
  }
}

// NOT USING! ONLY AFTER LAST IMAGES
async function addRandomFieldToExistingImages() {
  try {
    // Obtener todos los documentos de la colección "images"
    const imagesCollection = collection(db, "images");
    const querySnapshot = await getDocs(imagesCollection);

    const batchPromises = querySnapshot.docs.map(async (imageDoc) => {
      const docRef = doc(db, "images", imageDoc.id);

      // Actualizar cada documento con un campo "random"
      await updateDoc(docRef, {
        random: Math.random(), // Generar un valor aleatorio para cada documento
      });
    });

    // Esperar a que todas las actualizaciones se completen
    await Promise.all(batchPromises);

    console.log(
      "Todos los documentos han sido actualizados con el campo random."
    );
  } catch (error) {
    console.error(
      "Error al añadir el campo random a los documentos existentes:",
      error
    );
  }
}
</script>

<style scoped>
.restart-button {
  position: fixed;
  bottom: 10px;
  right: 20px;
  z-index: 1000;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.restart-button:hover {
  background-color: #f1f1f1;
}
</style>
