<template>
  
          <!-- v-if="$q.screen.gt.xs" -->

  <q-layout view="hHh lpR fFf" class="layout-container">
    <div class="background-image"></div>
    <q-header
      elevated
      class="bg-secondary bordered text-primary"
      height-hint="64"
    >
      <q-toolbar class="GNL__toolbar">
        <q-btn
          flat
          dense
          round
          @click="toggleLeftDrawer"
          aria-label="Menu"
          class="q-mr-xs"
          size="xl"
        >
          <q-img src="./../assets/logo.png" width="42px"></q-img>
        </q-btn>
        <!-- v-if="$q.screen.gt.xs" -->
        <q-btn
          flat
          class="row items-center no-wrap text-weight-bold"
          label="Trivial60"
          to="/"
          size="lg"
        >
        </q-btn>

        <q-space />

        <div v-if="userName" class="q-gutter-sm row items-center no-wrap">
          <div class="row items-center q-ml-md">
            <q-avatar
              size="40px"
              font-size="18px"
              color="primary"
              text-color="white"
            >
              {{ userInitials }}
            </q-avatar>
          </div>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="bg-secondary"
      :width="280"
    >
      <q-scroll-area class="fit">
        <q-list padding class="text-black">
          <router-link
            v-for="link in links1"
            :key="link.text"
            :to="link.to"
            class="GNL__drawer-item text-h6"
          >
            <q-item clickable v-ripple>
              <q-item-section avatar>
                <q-icon :name="link.icon" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.text }}</q-item-label>
              </q-item-section>
            </q-item>
          </router-link>

          <div class="q-mt-xl">
            <div class="flex q-pl-md column q-gutter-xs text-h6">
              <router-link
                class="GNL__drawer-footer-link"
                to="/rules"
                aria-label="About"
                >Reglas
              </router-link>

              <router-link
                class="GNL__drawer-footer-link"
                to="/about"
                aria-label="About"
                >Sobre Trivial60
              </router-link>
              <!-- <router-link
                class="GNL__drawer-footer-link"
                to="/privacy"
                aria-label="About"
                >Política de privacidad
              </router-link> -->
            </div>
          </div>
        </q-list>
        <div class="bottom-right-text">
          Creado por barrioviajero.com - Versión 1
        </div>
      </q-scroll-area>
    </q-drawer>

    <!-- Sistema de desbloqueo -->
    <!-- <div v-if="!isUnlocked" class="flex flex-center">
      <q-card class="q-pa-md">
        <q-card-section>
          <h4>Introduce la contraseña maestra</h4>
        </q-card-section>
        <q-card-section>
          <q-input
            v-model="masterPasswordInput"
            type="password"
            label="Contraseña maestra"
            filled
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn color="primary" @click="validateMasterPassword">
            Desbloquear
          </q-btn>
        </q-card-actions>
      </q-card>
    </div> -->
    <!-- v-else -->
    <!-- v-if="$q.screen.gt.xs" -->

    <!-- <q-footer class="bg-transparent q-pa-md" >
      <q-tabs
        class="bg-primary shadow-4"
        style="border-radius: 30px"
        dense
      >
        <q-route-tab name="home" icon="home" label="Home" to="/"/>
        <q-route-tab name="jugar" icon="play_circle" label="Jugar" to="/play" />
        <q-route-tab name="ranking" icon="emoji_events" label="Ranking" to="/ranking"/>
        <q-route-tab name="about" icon="info" label="Sobre" to="/about"/>

      </q-tabs>
    </q-footer> -->

    <q-page-container class="content-wrapper">
      <router-view />
    </q-page-container>

    <!-- Texto en la esquina inferior derecha -->
  </q-layout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { auth, onAuthStateChanged } from "src/firebase";

// const MASTER_PASSWORD = "carroza";

const isUnlocked = ref(false);
const masterPasswordInput = ref("");
const leftDrawerOpen = ref(false);
const userName = ref(localStorage.getItem("localName") || "");

const links1 = [
  { icon: "play_circle", text: "Jugar", to: "/play" },
  { icon: "emoji_events", text: "Ranking", to: "/ranking" },
  // { icon: "play_arrow", text: "Jugar", to: "/play" },
  //{ icon: "person", text: "Mi perfil", to: "/profile" },
  //{ icon: "add", text: "Administrar cartas", to: "/create" },
];

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}

onMounted(() => {
  // const storedPassword = localStorage.getItem("masterPassword");
  // if (storedPassword === MASTER_PASSWORD) {
  //   isUnlocked.value = true;
  // }
  // onAuthStateChanged(auth, (currentUser) => {
  //   user.value = currentUser ? currentUser : null;
  // });
});

function validateMasterPassword() {
  if (masterPasswordInput.value === MASTER_PASSWORD) {
    isUnlocked.value = true;
    localStorage.setItem("masterPassword", MASTER_PASSWORD);
  } else {
  }
}

const userInitials = computed(() => {
  if (!userName.value) return ""; // Si no hay nombre, retorna vacío
  const names = userName.value.split(" ");
  return names.map((n) => n[0]?.toUpperCase()).join(""); // Obtener primeras letras y unirlas
});
</script>

<style scoped>
/* Contenedor principal para posicionar la imagen de fondo */
.layout-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%; /* Asegura que el layout cubra toda la pantalla */
}

/* Imagen de fondo */
.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("src/assets/maincover.jpeg");
  background-size: contain;
  background-position: center;
  filter: grayscale(15%);
  opacity: 0.2;
  z-index: -1;
}

/* Contenedor máximo para el contenido */
.content-wrapper {
  max-width: 1200px; /* Máximo ancho del contenido */
  margin: 0 auto; /* Centrar contenido horizontalmente */
  width: 100%; /* Asegura que ocupe el ancho completo en pantallas pequeñas */
  padding-right: 10px;
  padding-left: 10px;
}

/* Texto en la esquina inferior derecha */
.bottom-right-text {
  position: fixed;
  bottom: 8px;
  left: 8px;
  font-size: 0.75rem;
  color: #ffffff; /* Color blanco */
  background-color: rgba(0, 0, 0, 0.5); /* Fondo semitransparente */
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 2; /* Asegura que esté sobre otros elementos */
  margin-bottom: 10px;
}

a {
  text-decoration: none;
  color: inherit;
}
</style>
