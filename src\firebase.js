import { 
    initializeApp 
  } from "firebase/app";
  import { 
    getAuth, 
    GoogleAuthProvider, 
    signInWithPopup, 
    signOut, 
    createUserWithEmailAndPassword, 
    signInWithEmailAndPassword, 
    onAuthStateChanged // Importar onAuthStateChanged
  } from "firebase/auth";
  import { getFirestore } from "firebase/firestore";
  import { getStorage } from "firebase/storage";
  
  const firebaseConfig = {
    apiKey: "AIzaSyAjjaqRTcHD-aXgUY4wwTTw2tYiOcU1Zas",
    authDomain: "trivial-659e4.firebaseapp.com",
    projectId: "trivial-659e4",
    storageBucket: "trivial-659e4.appspot.com",
    messagingSenderId: "1050515378945",
    appId: "1:1050515378945:web:670b438c47e8bee37c8100",
  };
  
  const app = initializeApp(firebaseConfig);
  const auth = getAuth(app);
  const db = getFirestore(app);
  const storage = getStorage(app);
  const googleProvider = new GoogleAuthProvider();
  
  export { 
    auth, 
    db, 
    storage, 
    googleProvider, 
    signInWithPopup, 
    signOut, 
    createUserWithEmailAndPassword, 
    signInWithEmailAndPassword, 
    onAuthStateChanged // Exportar onAuthStateChanged
  };
  